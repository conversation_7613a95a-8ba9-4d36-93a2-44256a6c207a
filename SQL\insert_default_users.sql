-- =============================================
-- KelvinKMS - Insert Default Users
-- This script adds default admin and member users
-- =============================================

USE kelvinkms;

-- Insert default admin user (KMSAdmin)
-- Password: k1e9l9v9in (hashed with PHP password_hash)
INSERT INTO users (
    username, 
    password, 
    email, 
    first_name, 
    last_name, 
    nickname,
    gender,
    language,
    phone_number,
    street_address,
    city,
    state,
    zip_code,
    is_verified, 
    is_active, 
    is_admin,
    created_at
) VALUES (
    'KMSAdmin', 
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- k1e9l9v9in
    '<EMAIL>', 
    'KMS', 
    'Administrator',
    'Admin',
    'prefer_not_to_say',
    'en',
    '(*************',
    '123 Admin Street',
    'Los Angeles',
    'CA',
    '90210',
    TRUE, 
    TRUE, 
    TRUE,
    NOW()
) ON DUPLICATE KEY UPDATE 
    password = VALUES(password),
    email = VALUES(email),
    first_name = VALUES(first_name),
    last_name = VALUES(last_name),
    is_verified = VALUES(is_verified),
    is_active = VALUES(is_active),
    is_admin = VALUES(is_admin);

-- Insert default member user (KelvinKMS)
-- Password: k1e9l9v9in (hashed with PHP password_hash)
INSERT INTO users (
    username, 
    password, 
    email, 
    first_name, 
    last_name, 
    nickname,
    gender,
    birthday,
    language,
    phone_number,
    street_address,
    city,
    state,
    zip_code,
    is_verified, 
    is_active, 
    is_admin,
    created_at
) VALUES (
    'KelvinKMS', 
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- k1e9l9v9in
    '<EMAIL>', 
    'Kelvin', 
    'KMS',
    'Kelvin',
    'male',
    '1990-01-01',
    'en',
    '(*************',
    '456 Member Avenue',
    'Los Angeles',
    'CA',
    '90211',
    TRUE, 
    TRUE, 
    FALSE,
    NOW()
) ON DUPLICATE KEY UPDATE 
    password = VALUES(password),
    email = VALUES(email),
    first_name = VALUES(first_name),
    last_name = VALUES(last_name),
    is_verified = VALUES(is_verified),
    is_active = VALUES(is_active),
    is_admin = VALUES(is_admin);

-- Create wallets for both users
INSERT INTO user_wallets (user_id, balance, total_deposited) 
SELECT u.id, 1000.00, 1000.00 
FROM users u 
WHERE u.username = 'KMSAdmin'
ON DUPLICATE KEY UPDATE 
    balance = VALUES(balance),
    total_deposited = VALUES(total_deposited);

INSERT INTO user_wallets (user_id, balance, total_deposited) 
SELECT u.id, 100.00, 100.00 
FROM users u 
WHERE u.username = 'KelvinKMS'
ON DUPLICATE KEY UPDATE 
    balance = VALUES(balance),
    total_deposited = VALUES(total_deposited);

-- Create affiliate codes for both users
INSERT INTO affiliate_codes (user_id, affiliate_code, is_active)
SELECT u.id, 'ADMIN123', TRUE 
FROM users u 
WHERE u.username = 'KMSAdmin'
ON DUPLICATE KEY UPDATE 
    affiliate_code = VALUES(affiliate_code),
    is_active = VALUES(is_active);

INSERT INTO affiliate_codes (user_id, affiliate_code, is_active)
SELECT u.id, 'KELVIN123', TRUE 
FROM users u 
WHERE u.username = 'KelvinKMS'
ON DUPLICATE KEY UPDATE 
    affiliate_code = VALUES(affiliate_code),
    is_active = VALUES(is_active);

-- Add initial credit transactions for wallet setup
INSERT INTO credit_transactions (
    transaction_id,
    user_id,
    transaction_type,
    amount,
    balance_before,
    balance_after,
    status,
    description,
    reference_type,
    created_at
)
SELECT 
    CONCAT('INIT_ADMIN_', UNIX_TIMESTAMP()),
    u.id,
    'admin_gift',
    1000.00,
    0.00,
    1000.00,
    'completed',
    'Initial admin wallet setup',
    'admin',
    NOW()
FROM users u 
WHERE u.username = 'KMSAdmin'
ON DUPLICATE KEY UPDATE transaction_id = transaction_id;

INSERT INTO credit_transactions (
    transaction_id,
    user_id,
    transaction_type,
    amount,
    balance_before,
    balance_after,
    status,
    description,
    reference_type,
    created_at
)
SELECT 
    CONCAT('INIT_MEMBER_', UNIX_TIMESTAMP()),
    u.id,
    'admin_gift',
    100.00,
    0.00,
    100.00,
    'completed',
    'Initial member wallet setup',
    'admin',
    NOW()
FROM users u 
WHERE u.username = 'KelvinKMS'
ON DUPLICATE KEY UPDATE transaction_id = transaction_id;

-- Display results
SELECT 'Default users created successfully!' as message;
SELECT username, email, is_admin, is_verified, is_active FROM users WHERE username IN ('KMSAdmin', 'KelvinKMS');
SELECT u.username, w.balance, w.total_deposited FROM users u JOIN user_wallets w ON u.id = w.user_id WHERE u.username IN ('KMSAdmin', 'KelvinKMS');
SELECT u.username, a.affiliate_code, a.is_active FROM users u JOIN affiliate_codes a ON u.id = a.user_id WHERE u.username IN ('KMSAdmin', 'KelvinKMS');
