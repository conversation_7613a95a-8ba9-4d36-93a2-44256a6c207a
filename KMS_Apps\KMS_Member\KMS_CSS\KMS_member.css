/* KMS Member Dashboard Styles */
body { 
    font-family: Arial, sans-serif; 
    background-color: #a48f19; 
    color: white; 
    margin: 0; 
    padding: 20px; 
}

.container { 
    max-width: 1000px; 
    margin: auto; 
    background-color: #2b9869; 
    padding: 20px; 
    border-radius: 14px; 
    box-shadow: 0 5px 15px rgba(0,0,0,0.5); 
}

h1, h2 { 
    color: #00ffff; 
    text-align: center; 
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
}

h3, h4, h5, h6 { 
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8); 
}

p, div, span { 
    text-shadow: 1px 1px 2px rgba(0,0,0,0.6); 
}

#welcome-msg { 
    text-align: center; 
    font-size: 24px; 
    margin-bottom: 20px; 
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8); 
}

/* Wallet Section Styles */
.wallet-section { 
    margin: 20px 0; 
}

.wallet-card { 
    background: rgba(19,73,240,0.4); 
    padding: 10px; 
    border-radius: 10px; 
    margin-bottom: 20px; 
}

.wallet-balance { 
    text-align: center; 
    margin-bottom: 20px; 
}

.balance-amount { 
    font-size: 36px; 
    font-weight: bold; 
    color: #ffd700; 
    margin: 10px 0; 
}

.wallet-stats { 
    display: flex; 
    justify-content: space-around; 
    margin: 20px 0; 
    flex-wrap: wrap; 
    gap: 15px; 
}

.stat-item { 
    text-align: center; 
    flex: 1; 
    min-width: 150px; 
}

.stat-item h4 { 
    color: #00ffff; 
    margin: 0 0 10px 0; 
    font-size: 14px; 
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8); 
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #ffd700;
    background: linear-gradient(145deg, rgba(255,215,0,0.1), rgba(255,215,0,0.05));
    border: 2px solid rgba(255,215,0,0.3);
    border-radius: 10px;
    padding: 15px 10px;
    margin: 5px 0;
    box-shadow:
        0 4px 8px rgba(0,0,0,0.3),
        inset 0 1px 0 rgba(255,255,255,0.1),
        inset 0 -1px 0 rgba(0,0,0,0.2);
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stat-value::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s;
}

.stat-value:hover {
    transform: translateY(-2px);
    box-shadow:
        0 6px 12px rgba(0,0,0,0.4),
        inset 0 1px 0 rgba(255,255,255,0.2),
        inset 0 -1px 0 rgba(0,0,0,0.3);
}

.stat-value:hover::before {
    left: 100%;
}

.wallet-actions { 
    display: flex; 
    justify-content: center; 
    gap: 15px; 
    margin-top: 20px; 
}

/* Button Styles */
button {
    cursor: pointer;
    padding: 10px 15px;
    border: none;
    border-radius: 10px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.4);
}

.btn-deposit {
    background: linear-gradient(145deg, #4CAF50, #45a049);
    color: white;
}

.btn-withdraw {
    background: linear-gradient(145deg, #f44336, #da190b);
    color: white;
}

.btn-transfer {
    background: linear-gradient(145deg, #2196F3, #0b7dda);
    color: white;
}

.btn-primary {
    background: linear-gradient(145deg, #2196F3, #0b7dda);
    color: white;
}

.btn-success {
    background: linear-gradient(145deg, #4CAF50, #45a049);
    color: white;
}

.btn-warning {
    background: linear-gradient(145deg, #ff9800, #e68900);
    color: white;
}

.btn-danger {
    background: linear-gradient(145deg, #f44336, #da190b);
    color: white;
}

/* Service Section Styles */
.service-section {
    margin: 30px 0;
    background: rgba(0,0,0,0.2);
    padding: 20px;
    border-radius: 10px;
}

.service-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.service-card {
    background: rgba(255,255,255,0.1);
    padding: 20px;
    border-radius: 10px;
    border: 2px solid rgba(255,255,255,0.2);
    transition: all 0.3s ease;
}

.service-card:hover {
    background: rgba(255,255,255,0.15);
    border-color: rgba(255,255,255,0.4);
    transform: translateY(-5px);
}

.service-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.service-item:last-child {
    border-bottom: none;
}

.service-name {
    font-weight: bold;
    color: #00ffff;
}

.service-price {
    color: #ffd700;
    font-weight: bold;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 10px;
}

.quantity-btn {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #2196F3;
    color: white;
    border: none;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quantity-input {
    width: 60px;
    text-align: center;
    padding: 5px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background: rgba(255,255,255,0.9);
    color: #333;
}

/* Order Summary Styles */
.order-summary {
    background: rgba(0,0,0,0.3);
    padding: 20px;
    border-radius: 10px;
    margin-top: 20px;
}

.order-total {
    font-size: 24px;
    font-weight: bold;
    color: #ffd700;
    text-align: center;
    margin: 20px 0;
}

/* PC Builder Styles */
.pc-builder-section {
    margin: 30px 0;
}

.mode-selector {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
}

.mode-btn {
    padding: 15px 30px;
    font-size: 16px;
    font-weight: bold;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mode-btn.active {
    background: linear-gradient(145deg, #4CAF50, #45a049);
    color: white;
    transform: scale(1.05);
}

.mode-btn:not(.active) {
    background: rgba(255,255,255,0.2);
    color: #ccc;
}

.pc-component-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.component-card {
    background: rgba(255,255,255,0.1);
    padding: 15px;
    border-radius: 10px;
    border: 2px solid rgba(255,255,255,0.2);
}

.component-select {
    width: 100%;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #ccc;
    background: rgba(255,255,255,0.9);
    color: #333;
    margin-top: 10px;
}

/* Affiliate Section Styles */
.affiliate-section {
    margin: 30px 0;
}

.affiliate-code-display {
    background: rgba(0,0,0,0.3);
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    margin: 20px 0;
}

.affiliate-code {
    font-size: 24px;
    font-weight: bold;
    color: #ffd700;
    background: rgba(255,215,0,0.1);
    padding: 10px 20px;
    border-radius: 5px;
    display: inline-block;
    margin: 10px 0;
}

.referral-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.referral-stat-card {
    background: rgba(255,255,255,0.1);
    padding: 20px;
    border-radius: 10px;
    text-align: center;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: #2b9869;
    margin: 5% auto;
    padding: 20px;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    position: relative;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: white;
}

/* Form Styles */
.form-group {
    margin: 15px 0;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #00ffff;
    font-weight: bold;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background: rgba(255,255,255,0.9);
    color: #333;
    box-sizing: border-box;
}

.form-group textarea {
    background-color: #ffc000;
    resize: vertical;
    min-height: 100px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
        margin: 10px;
    }
    
    .wallet-stats {
        flex-direction: column;
    }
    
    .service-grid {
        grid-template-columns: 1fr;
    }
    
    .mode-selector {
        flex-direction: column;
        align-items: center;
    }
    
    .pc-component-grid {
        grid-template-columns: 1fr;
    }
}

/* Scrollbar Styles */
::-webkit-scrollbar {
    width: 3px;
}

::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.1);
}

::-webkit-scrollbar-thumb {
    background: #2b9869;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #1e6b4a;
}
